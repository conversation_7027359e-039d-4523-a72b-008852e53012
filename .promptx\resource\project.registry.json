{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T15:02:52.191Z", "updatedAt": "2025-07-30T15:02:52.369Z", "resourceCount": 48}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.196Z", "updatedAt": "2025-07-30T15:02:52.196Z", "scannedAt": "2025-07-30T15:02:52.196Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.198Z", "updatedAt": "2025-07-30T15:02:52.198Z", "scannedAt": "2025-07-30T15:02:52.198Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.200Z", "updatedAt": "2025-07-30T15:02:52.200Z", "scannedAt": "2025-07-30T15:02:52.200Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.202Z", "updatedAt": "2025-07-30T15:02:52.202Z", "scannedAt": "2025-07-30T15:02:52.202Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.204Z", "updatedAt": "2025-07-30T15:02:52.204Z", "scannedAt": "2025-07-30T15:02:52.204Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.205Z", "updatedAt": "2025-07-30T15:02:52.205Z", "scannedAt": "2025-07-30T15:02:52.205Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.210Z", "updatedAt": "2025-07-30T15:02:52.211Z", "scannedAt": "2025-07-30T15:02:52.210Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.222Z", "updatedAt": "2025-07-30T15:02:52.222Z", "scannedAt": "2025-07-30T15:02:52.222Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.225Z", "updatedAt": "2025-07-30T15:02:52.225Z", "scannedAt": "2025-07-30T15:02:52.225Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "data-visualization-expert", "source": "project", "protocol": "role", "name": "Data Visualization Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/data-visualization-expert/data-visualization-expert.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.228Z", "updatedAt": "2025-07-30T15:02:52.228Z", "scannedAt": "2025-07-30T15:02:52.228Z", "path": "role/data-visualization-expert/data-visualization-expert.role.md"}}, {"id": "visualization-development", "source": "project", "protocol": "execution", "name": "Visualization Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-visualization-expert/execution/visualization-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.231Z", "updatedAt": "2025-07-30T15:02:52.231Z", "scannedAt": "2025-07-30T15:02:52.231Z", "path": "role/data-visualization-expert/execution/visualization-development.execution.md"}}, {"id": "data-visualization", "source": "project", "protocol": "thought", "name": "Data Visualization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-visualization-expert/thought/data-visualization.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.233Z", "updatedAt": "2025-07-30T15:02:52.233Z", "scannedAt": "2025-07-30T15:02:52.233Z", "path": "role/data-visualization-expert/thought/data-visualization.thought.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.234Z", "updatedAt": "2025-07-30T15:02:52.234Z", "scannedAt": "2025-07-30T15:02:52.234Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "database-development", "source": "project", "protocol": "execution", "name": "Database Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/database-architect/execution/database-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.244Z", "updatedAt": "2025-07-30T15:02:52.244Z", "scannedAt": "2025-07-30T15:02:52.244Z", "path": "role/database-architect/execution/database-development.execution.md"}}, {"id": "database-design", "source": "project", "protocol": "thought", "name": "Database Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/database-architect/thought/database-design.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.247Z", "updatedAt": "2025-07-30T15:02:52.247Z", "scannedAt": "2025-07-30T15:02:52.247Z", "path": "role/database-architect/thought/database-design.thought.md"}}, {"id": "<PERSON><PERSON><PERSON>-engineer", "source": "project", "protocol": "role", "name": "Devops Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/devops-engineer/devops-engineer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.249Z", "updatedAt": "2025-07-30T15:02:52.249Z", "scannedAt": "2025-07-30T15:02:52.249Z", "path": "role/devops-engineer/devops-engineer.role.md"}}, {"id": "devops-development", "source": "project", "protocol": "execution", "name": "Devops Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/devops-engineer/execution/devops-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.251Z", "updatedAt": "2025-07-30T15:02:52.251Z", "scannedAt": "2025-07-30T15:02:52.251Z", "path": "role/devops-engineer/execution/devops-development.execution.md"}}, {"id": "devops-automation", "source": "project", "protocol": "thought", "name": "Devops Automation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/devops-engineer/thought/devops-automation.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.252Z", "updatedAt": "2025-07-30T15:02:52.252Z", "scannedAt": "2025-07-30T15:02:52.252Z", "path": "role/devops-engineer/thought/devops-automation.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.254Z", "updatedAt": "2025-07-30T15:02:52.254Z", "scannedAt": "2025-07-30T15:02:52.254Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.258Z", "updatedAt": "2025-07-30T15:02:52.258Z", "scannedAt": "2025-07-30T15:02:52.258Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.270Z", "updatedAt": "2025-07-30T15:02:52.270Z", "scannedAt": "2025-07-30T15:02:52.270Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "electron-developer", "source": "project", "protocol": "role", "name": "Electron Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electron-developer/electron-developer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.271Z", "updatedAt": "2025-07-30T15:02:52.271Z", "scannedAt": "2025-07-30T15:02:52.271Z", "path": "role/electron-developer/electron-developer.role.md"}}, {"id": "electron-development", "source": "project", "protocol": "execution", "name": "Electron Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electron-developer/execution/electron-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.280Z", "updatedAt": "2025-07-30T15:02:52.280Z", "scannedAt": "2025-07-30T15:02:52.280Z", "path": "role/electron-developer/execution/electron-development.execution.md"}}, {"id": "electron-architecture", "source": "project", "protocol": "thought", "name": "Electron Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electron-developer/thought/electron-architecture.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.281Z", "updatedAt": "2025-07-30T15:02:52.281Z", "scannedAt": "2025-07-30T15:02:52.281Z", "path": "role/electron-developer/thought/electron-architecture.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.283Z", "updatedAt": "2025-07-30T15:02:52.283Z", "scannedAt": "2025-07-30T15:02:52.283Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.284Z", "updatedAt": "2025-07-30T15:02:52.284Z", "scannedAt": "2025-07-30T15:02:52.284Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.286Z", "updatedAt": "2025-07-30T15:02:52.286Z", "scannedAt": "2025-07-30T15:02:52.286Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "nlp-development", "source": "project", "protocol": "execution", "name": "Nlp Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/nlp-expert/execution/nlp-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.288Z", "updatedAt": "2025-07-30T15:02:52.288Z", "scannedAt": "2025-07-30T15:02:52.288Z", "path": "role/nlp-expert/execution/nlp-development.execution.md"}}, {"id": "nlp-expert", "source": "project", "protocol": "role", "name": "Nlp Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/nlp-expert/nlp-expert.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.289Z", "updatedAt": "2025-07-30T15:02:52.289Z", "scannedAt": "2025-07-30T15:02:52.289Z", "path": "role/nlp-expert/nlp-expert.role.md"}}, {"id": "nlp-processing", "source": "project", "protocol": "thought", "name": "Nlp Processing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/nlp-expert/thought/nlp-processing.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.293Z", "updatedAt": "2025-07-30T15:02:52.293Z", "scannedAt": "2025-07-30T15:02:52.293Z", "path": "role/nlp-expert/thought/nlp-processing.thought.md"}}, {"id": "performance-development", "source": "project", "protocol": "execution", "name": "Performance Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/performance-expert/execution/performance-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.296Z", "updatedAt": "2025-07-30T15:02:52.296Z", "scannedAt": "2025-07-30T15:02:52.296Z", "path": "role/performance-expert/execution/performance-development.execution.md"}}, {"id": "performance-expert", "source": "project", "protocol": "role", "name": "Performance Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/performance-expert/performance-expert.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.298Z", "updatedAt": "2025-07-30T15:02:52.298Z", "scannedAt": "2025-07-30T15:02:52.298Z", "path": "role/performance-expert/performance-expert.role.md"}}, {"id": "performance-optimization", "source": "project", "protocol": "thought", "name": "Performance Optimization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/performance-expert/thought/performance-optimization.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.299Z", "updatedAt": "2025-07-30T15:02:52.299Z", "scannedAt": "2025-07-30T15:02:52.299Z", "path": "role/performance-expert/thought/performance-optimization.thought.md"}}, {"id": "editor-development", "source": "project", "protocol": "execution", "name": "Editor Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/execution/editor-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.302Z", "updatedAt": "2025-07-30T15:02:52.302Z", "scannedAt": "2025-07-30T15:02:52.302Z", "path": "role/rich-text-editor-expert/execution/editor-development.execution.md"}}, {"id": "rich-text-editor-expert", "source": "project", "protocol": "role", "name": "Rich Text Editor Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/rich-text-editor-expert.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.302Z", "updatedAt": "2025-07-30T15:02:52.302Z", "scannedAt": "2025-07-30T15:02:52.302Z", "path": "role/rich-text-editor-expert/rich-text-editor-expert.role.md"}}, {"id": "rich-text-editing", "source": "project", "protocol": "thought", "name": "Rich Text Editing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/thought/rich-text-editing.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.304Z", "updatedAt": "2025-07-30T15:02:52.304Z", "scannedAt": "2025-07-30T15:02:52.304Z", "path": "role/rich-text-editor-expert/thought/rich-text-editing.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.308Z", "updatedAt": "2025-07-30T15:02:52.308Z", "scannedAt": "2025-07-30T15:02:52.308Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.309Z", "updatedAt": "2025-07-30T15:02:52.309Z", "scannedAt": "2025-07-30T15:02:52.309Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.314Z", "updatedAt": "2025-07-30T15:02:52.314Z", "scannedAt": "2025-07-30T15:02:52.314Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.315Z", "updatedAt": "2025-07-30T15:02:52.315Z", "scannedAt": "2025-07-30T15:02:52.315Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.355Z", "updatedAt": "2025-07-30T15:02:52.355Z", "scannedAt": "2025-07-30T15:02:52.355Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.359Z", "updatedAt": "2025-07-30T15:02:52.359Z", "scannedAt": "2025-07-30T15:02:52.359Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.360Z", "updatedAt": "2025-07-30T15:02:52.360Z", "scannedAt": "2025-07-30T15:02:52.360Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.362Z", "updatedAt": "2025-07-30T15:02:52.362Z", "scannedAt": "2025-07-30T15:02:52.362Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.363Z", "updatedAt": "2025-07-30T15:02:52.363Z", "scannedAt": "2025-07-30T15:02:52.363Z", "path": "role/system-director/thought/team-coordination.thought.md"}}, {"id": "testing-development", "source": "project", "protocol": "execution", "name": "Testing Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/test-engineer/execution/testing-development.execution.md", "metadata": {"createdAt": "2025-07-30T15:02:52.365Z", "updatedAt": "2025-07-30T15:02:52.365Z", "scannedAt": "2025-07-30T15:02:52.365Z", "path": "role/test-engineer/execution/testing-development.execution.md"}}, {"id": "test-engineer", "source": "project", "protocol": "role", "name": "Test Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/test-engineer/test-engineer.role.md", "metadata": {"createdAt": "2025-07-30T15:02:52.366Z", "updatedAt": "2025-07-30T15:02:52.366Z", "scannedAt": "2025-07-30T15:02:52.366Z", "path": "role/test-engineer/test-engineer.role.md"}}, {"id": "testing-strategy", "source": "project", "protocol": "thought", "name": "Testing Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-engineer/thought/testing-strategy.thought.md", "metadata": {"createdAt": "2025-07-30T15:02:52.367Z", "updatedAt": "2025-07-30T15:02:52.367Z", "scannedAt": "2025-07-30T15:02:52.367Z", "path": "role/test-engineer/thought/testing-strategy.thought.md"}}], "stats": {"totalResources": 48, "byProtocol": {"role": 14, "execution": 17, "thought": 17}, "bySource": {"project": 48}}}